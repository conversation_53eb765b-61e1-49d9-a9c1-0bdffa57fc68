/*
 * Copyright 2023 The Tongsuo Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://github.com/Tongsuo-Project/tongsuo-mini/blob/main/LICENSE
 */

#if !defined(TSM_AES_H)
# define TSM_AES_H
# pragma once

# ifdef __cplusplus
extern "C" {
# endif

# include <stdlib.h>

# define TSM_AES_BLOCK_SIZE     16
# define TSM_AES_128_KEY_SIZE   16
# define TSM_AES_192_KEY_SIZE   24
# define TSM_AES_256_KEY_SIZE   32
# define TSM_AES_IV_SIZE        16

/* AES context structure */
typedef struct tsm_aes_ctx_st TSM_AES_CTX;

/* Create a new AES context. The context should be freed by calling tsm_aes_ctx_free() after use. */
TSM_AES_CTX *tsm_aes_ctx_new(void);

/* Free the AES context. */
void tsm_aes_ctx_free(TSM_AES_CTX *ctx);

/* Initialize the AES context.
 * Parameters:
 *   ctx: AES context
 *   mode: cipher mode (TSM_CIPH_MODE_*)
 *   key: encryption/decryption key (16, 24, or 32 bytes)
 *   key_len: key length in bytes
 *   iv: initialization vector (16 bytes, can be NULL for ECB mode)
 *   flags: operation flags (TSM_CIPH_FLAG_*)
 * Returns TSM_OK on success, error code on failure.
 */
int tsm_aes_init(TSM_AES_CTX *ctx, int mode, const unsigned char *key, size_t key_len,
                 const unsigned char *iv, int flags);

/* Update the AES context with input data.
 * Parameters:
 *   ctx: AES context
 *   in: input data
 *   inl: input data length
 *   out: output buffer
 *   outl: pointer to output length (updated with actual output length)
 * Returns TSM_OK on success, error code on failure.
 */
int tsm_aes_update(TSM_AES_CTX *ctx, const unsigned char *in, size_t inl,
                   unsigned char *out, size_t *outl);

/* Finalize the AES operation.
 * Parameters:
 *   ctx: AES context
 *   out: output buffer for final block
 *   outl: pointer to output length (updated with actual output length)
 * Returns TSM_OK on success, error code on failure.
 */
int tsm_aes_final(TSM_AES_CTX *ctx, unsigned char *out, size_t *outl);

/* One-shot AES operation.
 * Parameters:
 *   mode: cipher mode (TSM_CIPH_MODE_*)
 *   key: encryption/decryption key
 *   key_len: key length (16, 24, or 32 bytes)
 *   iv: initialization vector (16 bytes, can be NULL for ECB mode)
 *   in: input data
 *   inl: input data length
 *   out: output buffer
 *   outl: pointer to output length (updated with actual output length)
 *   flags: operation flags (TSM_CIPH_FLAG_*)
 * Returns TSM_OK on success, error code on failure.
 */
int tsm_aes_oneshot(int mode, const unsigned char *key, size_t key_len,
                    const unsigned char *iv, const unsigned char *in, size_t inl,
                    unsigned char *out, size_t *outl, int flags);

/* AES-GCM specific functions */

/* Initialize AES-GCM context with additional authenticated data (AAD).
 * This function should be called after tsm_aes_init() for GCM mode.
 * Parameters:
 *   ctx: AES context (must be initialized for GCM mode)
 *   aad: additional authenticated data (can be NULL if aad_len is 0)
 *   aad_len: AAD length in bytes
 * Returns TSM_OK on success, error code on failure.
 */
int tsm_aes_gcm_set_aad(TSM_AES_CTX *ctx, const unsigned char *aad, size_t aad_len);

/* Get the authentication tag for AES-GCM.
 * This function should be called after encryption is complete.
 * Parameters:
 *   ctx: AES context (must be in GCM mode)
 *   tag: buffer to store the authentication tag
 *   tag_len: tag length in bytes (typically 16)
 * Returns TSM_OK on success, error code on failure.
 */
int tsm_aes_gcm_get_tag(TSM_AES_CTX *ctx, unsigned char *tag, size_t tag_len);

/* Set the authentication tag for AES-GCM verification.
 * This function should be called before decryption.
 * Parameters:
 *   ctx: AES context (must be in GCM mode)
 *   tag: authentication tag to verify
 *   tag_len: tag length in bytes
 * Returns TSM_OK on success, error code on failure.
 */
int tsm_aes_gcm_set_tag(TSM_AES_CTX *ctx, const unsigned char *tag, size_t tag_len);

/* AES-CCM specific functions */

/* Initialize AES-CCM context with parameters.
 * This function should be called after tsm_aes_init() for CCM mode.
 * Parameters:
 *   ctx: AES context (must be initialized for CCM mode)
 *   aad: additional authenticated data (can be NULL if aad_len is 0)
 *   aad_len: AAD length in bytes
 *   tag_len: authentication tag length (4, 6, 8, 10, 12, 14, or 16 bytes)
 * Returns TSM_OK on success, error code on failure.
 */
int tsm_aes_ccm_set_params(TSM_AES_CTX *ctx, const unsigned char *aad, size_t aad_len, size_t tag_len);

/* Get the authentication tag for AES-CCM.
 * This function should be called after encryption is complete.
 * Parameters:
 *   ctx: AES context (must be in CCM mode)
 *   tag: buffer to store the authentication tag
 *   tag_len: tag length in bytes
 * Returns TSM_OK on success, error code on failure.
 */
int tsm_aes_ccm_get_tag(TSM_AES_CTX *ctx, unsigned char *tag, size_t tag_len);

/* Set the authentication tag for AES-CCM verification.
 * This function should be called before decryption.
 * Parameters:
 *   ctx: AES context (must be in CCM mode)
 *   tag: authentication tag to verify
 *   tag_len: tag length in bytes
 * Returns TSM_OK on success, error code on failure.
 */
int tsm_aes_ccm_set_tag(TSM_AES_CTX *ctx, const unsigned char *tag, size_t tag_len);

# ifdef __cplusplus
}
# endif
#endif
