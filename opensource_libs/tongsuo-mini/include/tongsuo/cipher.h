/*
 * Copyright 2023 The Tongsuo Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://github.com/Tongsuo-Project/tongsuo-mini/blob/main/LICENSE
 */

#if !defined(TSM_CIPHER_H)
# define TSM_CIPHER_H
# pragma once

# ifdef __cplusplus
extern "C" {
# endif

# include <stdlib.h>
# include <tongsuo/minisuo.h>

/* Generic cipher context structure */
typedef struct tsm_cipher_ctx_st TSM_CIPHER_CTX;

/* Create a new cipher context. The context should be freed by calling tsm_cipher_ctx_free() after use. */
TSM_CIPHER_CTX *tsm_cipher_ctx_new(void);

/* Free the cipher context. */
void tsm_cipher_ctx_free(TSM_CIPHER_CTX *ctx);

/* Initialize the cipher context with specified algorithm and mode.
 * Parameters:
 *   ctx: cipher context
 *   alg: cipher algorithm (TSM_CIPHER_*)
 *   mode: cipher mode (TSM_CIPH_MODE_*)
 *   key: encryption/decryption key
 *   key_len: key length in bytes
 *   iv: initialization vector (can be NULL for ECB mode)
 *   iv_len: IV length in bytes
 *   flags: operation flags (TSM_CIPH_FLAG_*)
 * Returns TSM_OK on success, error code on failure.
 */
int tsm_cipher_init(TSM_CIPHER_CTX *ctx, int alg, int mode,
                    const unsigned char *key, size_t key_len,
                    const unsigned char *iv, size_t iv_len, int flags);

/* Update the cipher context with input data.
 * Parameters:
 *   ctx: cipher context
 *   in: input data
 *   inl: input data length
 *   out: output buffer
 *   outl: pointer to output length (updated with actual output length)
 * Returns TSM_OK on success, error code on failure.
 */
int tsm_cipher_update(TSM_CIPHER_CTX *ctx, const unsigned char *in, size_t inl,
                      unsigned char *out, size_t *outl);

/* Finalize the cipher operation.
 * Parameters:
 *   ctx: cipher context
 *   out: output buffer for final block
 *   outl: pointer to output length (updated with actual output length)
 * Returns TSM_OK on success, error code on failure.
 */
int tsm_cipher_final(TSM_CIPHER_CTX *ctx, unsigned char *out, size_t *outl);

/* One-shot cipher operation.
 * Parameters:
 *   alg: cipher algorithm (TSM_CIPHER_*)
 *   mode: cipher mode (TSM_CIPH_MODE_*)
 *   key: encryption/decryption key
 *   key_len: key length in bytes
 *   iv: initialization vector (can be NULL for ECB mode)
 *   iv_len: IV length in bytes
 *   in: input data
 *   inl: input data length
 *   out: output buffer
 *   outl: pointer to output length (updated with actual output length)
 *   flags: operation flags (TSM_CIPH_FLAG_*)
 * Returns TSM_OK on success, error code on failure.
 */
int tsm_cipher_oneshot(int alg, int mode,
                       const unsigned char *key, size_t key_len,
                       const unsigned char *iv, size_t iv_len,
                       const unsigned char *in, size_t inl,
                       unsigned char *out, size_t *outl, int flags);

/* Get cipher block size for specified algorithm.
 * Returns block size in bytes, or 0 for stream ciphers.
 */
int tsm_cipher_get_block_size(int alg);

/* Get cipher key length for specified algorithm.
 * Returns key length in bytes, or 0 on error.
 */
int tsm_cipher_get_key_length(int alg);

/* Get cipher IV length for specified algorithm and mode.
 * Returns IV length in bytes, or 0 for modes that don't use IV.
 */
int tsm_cipher_get_iv_length(int alg, int mode);

/* Check if algorithm and mode combination is supported.
 * Returns 1 if supported, 0 if not supported.
 */
int tsm_cipher_is_supported(int alg, int mode);

# ifdef __cplusplus
}
# endif
#endif
