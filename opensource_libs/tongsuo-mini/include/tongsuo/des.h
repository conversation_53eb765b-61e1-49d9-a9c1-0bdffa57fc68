/*
 * Copyright 2023 The Tongsuo Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://github.com/Tongsuo-Project/tongsuo-mini/blob/main/LICENSE
 */

#if !defined(TSM_DES_H)
# define TSM_DES_H
# pragma once

# ifdef __cplusplus
extern "C" {
# endif

# include <stdlib.h>

# define TSM_DES_BLOCK_SIZE     8
# define TSM_DES_KEY_SIZE       8
# define TSM_3DES_EDE2_KEY_SIZE 16
# define TSM_3DES_EDE3_KEY_SIZE 24

/* DES context structure */
typedef struct tsm_des_ctx_st TSM_DES_CTX;

/* Create a new DES context. The context should be freed by calling tsm_des_ctx_free() after use. */
TSM_DES_CTX *tsm_des_ctx_new(void);

/* Free the DES context. */
void tsm_des_ctx_free(TSM_DES_CTX *ctx);

/* Initialize the DES context.
 * Parameters:
 *   ctx: DES context
 *   mode: cipher mode (TSM_CIPH_MODE_*)
 *   key: encryption/decryption key (8 bytes for DES, 16 bytes for 3DES-EDE2, 24 bytes for 3DES-EDE3)
 *   iv: initialization vector (8 bytes, can be NULL for ECB mode)
 *   flags: operation flags (TSM_CIPH_FLAG_*)
 * Returns TSM_OK on success, error code on failure.
 */
int tsm_des_init(TSM_DES_CTX *ctx, int mode, const unsigned char *key,
                 const unsigned char *iv, int flags);

/* Update the DES context with input data.
 * Parameters:
 *   ctx: DES context
 *   in: input data
 *   inl: input data length
 *   out: output buffer
 *   outl: pointer to output length (updated with actual output length)
 * Returns TSM_OK on success, error code on failure.
 */
int tsm_des_update(TSM_DES_CTX *ctx, const unsigned char *in, size_t inl,
                   unsigned char *out, size_t *outl);

/* Finalize the DES operation.
 * Parameters:
 *   ctx: DES context
 *   out: output buffer for final block
 *   outl: pointer to output length (updated with actual output length)
 * Returns TSM_OK on success, error code on failure.
 */
int tsm_des_final(TSM_DES_CTX *ctx, unsigned char *out, size_t *outl);

/* One-shot DES operation.
 * Parameters:
 *   mode: cipher mode (TSM_CIPH_MODE_*)
 *   key: encryption/decryption key
 *   key_len: key length (8, 16, or 24 bytes)
 *   iv: initialization vector (8 bytes, can be NULL for ECB mode)
 *   in: input data
 *   inl: input data length
 *   out: output buffer
 *   outl: pointer to output length (updated with actual output length)
 *   flags: operation flags (TSM_CIPH_FLAG_*)
 * Returns TSM_OK on success, error code on failure.
 */
int tsm_des_oneshot(int mode, const unsigned char *key, size_t key_len,
                    const unsigned char *iv, const unsigned char *in, size_t inl,
                    unsigned char *out, size_t *outl, int flags);

/* Check if a DES key is weak.
 * Returns 1 if the key is weak, 0 if it's strong.
 */
int tsm_des_is_weak_key(const unsigned char *key);

/* Set odd parity for a DES key.
 * The key buffer will be modified in place.
 */
void tsm_des_set_odd_parity(unsigned char *key);

/* Check if a DES key has odd parity.
 * Returns 1 if the key has odd parity, 0 otherwise.
 */
int tsm_des_check_key_parity(const unsigned char *key);

# ifdef __cplusplus
}
# endif
#endif
