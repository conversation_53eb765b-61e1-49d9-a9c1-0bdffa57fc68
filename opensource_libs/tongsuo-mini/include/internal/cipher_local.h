/*
 * Copyright 2023 The Tongsuo Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://github.com/Tongsuo-Project/tongsuo-mini/blob/main/LICENSE
 */

#if !defined(TSM_INTERNAL_CIPHER_LOCAL_H)
# define TSM_INTERNAL_CIPHER_LOCAL_H
# pragma once

# include <stdint.h>
# include <stdlib.h>
# include <tongsuo/minisuo.h>

/* Cipher method structure for algorithm registration */
typedef struct {
    const char *name;
    uint8_t alg;
    uint8_t key_len;
    uint8_t block_size;
    uint8_t iv_len;
    uint32_t supported_modes;  /* Bitmask of supported modes */
    void *(*newctx)(void);
    void (*freectx)(void *ctx);
    int (*init)(void *ctx, int mode, const unsigned char *key, 
                const unsigned char *iv, int flags);
    int (*update)(void *ctx, const unsigned char *in, size_t inl,
                  unsigned char *out, size_t *outl);
    int (*final)(void *ctx, unsigned char *out, size_t *outl);
    int (*set_key)(void *ctx, const unsigned char *key, size_t key_len);
    int (*set_iv)(void *ctx, const unsigned char *iv, size_t iv_len);
} TSM_CIPHER_METH;

/* Generic cipher context structure */
struct tsm_cipher_ctx_st {
    const TSM_CIPHER_METH *meth;
    void *cipher_data;
    int alg;
    int mode;
    int flags;
    int initialized;
    unsigned char key[TSM_MAX_KEY_LENGTH];
    unsigned char iv[TSM_MAX_IV_LENGTH];
    size_t key_len;
    size_t iv_len;
};

/* Mode support flags */
#define TSM_MODE_ECB_SUPPORT    (1 << TSM_CIPH_MODE_ECB)
#define TSM_MODE_CBC_SUPPORT    (1 << TSM_CIPH_MODE_CBC)
#define TSM_MODE_CFB_SUPPORT    (1 << TSM_CIPH_MODE_CFB)
#define TSM_MODE_OFB_SUPPORT    (1 << TSM_CIPH_MODE_OFB)
#define TSM_MODE_CTR_SUPPORT    (1 << TSM_CIPH_MODE_CTR)
#define TSM_MODE_GCM_SUPPORT    (1 << TSM_CIPH_MODE_GCM)
#define TSM_MODE_CCM_SUPPORT    (1 << TSM_CIPH_MODE_CCM)
#define TSM_MODE_XTS_SUPPORT    (1 << TSM_CIPH_MODE_XTS)

/* Common cipher operations */
#define TSM_CIPHER_BLOCK_SIZE_DES    8
#define TSM_CIPHER_BLOCK_SIZE_AES    16
#define TSM_CIPHER_BLOCK_SIZE_SM4    16

#define TSM_CIPHER_KEY_SIZE_DES      8
#define TSM_CIPHER_KEY_SIZE_3DES_EDE2 16
#define TSM_CIPHER_KEY_SIZE_3DES_EDE3 24
#define TSM_CIPHER_KEY_SIZE_AES_128  16
#define TSM_CIPHER_KEY_SIZE_AES_192  24
#define TSM_CIPHER_KEY_SIZE_AES_256  32
#define TSM_CIPHER_KEY_SIZE_SM4      16

/* Internal function declarations */
const TSM_CIPHER_METH *tsm_get_cipher_meth(int alg);
int tsm_cipher_validate_params(int alg, int mode, size_t key_len, size_t iv_len);
int tsm_cipher_apply_padding(unsigned char *buf, size_t buf_len, size_t data_len, size_t block_size);
int tsm_cipher_remove_padding(unsigned char *buf, size_t buf_len, size_t *data_len, size_t block_size);

/* DES/3DES specific structures and functions */
typedef struct {
    uint32_t ks[32];  /* Key schedule */
    int encrypt;      /* Encryption flag */
} TSM_DES_KEY;

typedef struct {
    TSM_DES_KEY ks1, ks2, ks3;  /* Three key schedules for 3DES */
    int encrypt;
} TSM_3DES_KEY;

typedef struct {
    union {
        TSM_DES_KEY des;
        TSM_3DES_KEY des3;
    } key;
    int mode;
    int flags;
    unsigned char iv[8];
    unsigned char buffer[8];
    size_t buffer_len;
    int is_3des;
} TSM_DES_CTX_INTERNAL;

/* AES specific structures and functions */
typedef struct {
    uint32_t rd_key[60];  /* Round keys */
    int rounds;           /* Number of rounds */
} TSM_AES_KEY;

typedef struct {
    TSM_AES_KEY key;
    int mode;
    int flags;
    unsigned char iv[16];
    unsigned char buffer[16];
    size_t buffer_len;
    /* Mode-specific data */
    union {
        struct {
            unsigned char counter[16];
            unsigned char keystream[16];
            size_t keystream_pos;
        } ctr;
        struct {
            unsigned char h[16];      /* Hash subkey */
            unsigned char tag[16];    /* Authentication tag */
            unsigned char j0[16];     /* Initial counter */
            size_t aad_len;
            size_t text_len;
        } gcm;
        struct {
            unsigned char tag[16];    /* Authentication tag */
            unsigned char b[16];      /* CBC-MAC state */
            size_t aad_len;
            size_t text_len;
            int tag_len;
        } ccm;
    } mode_data;
} TSM_AES_CTX;

/* Function declarations for specific algorithms */
#ifdef TSM_HAVE_DES
extern const TSM_CIPHER_METH tsm_des_meth;
extern const TSM_CIPHER_METH tsm_3des_ede2_meth;
extern const TSM_CIPHER_METH tsm_3des_ede3_meth;
#endif

#ifdef TSM_HAVE_AES
extern const TSM_CIPHER_METH tsm_aes_128_meth;
extern const TSM_CIPHER_METH tsm_aes_192_meth;
extern const TSM_CIPHER_METH tsm_aes_256_meth;
#endif

#endif
