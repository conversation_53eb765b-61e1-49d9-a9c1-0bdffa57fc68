# TEE_CheckMemoryAccessRights 设计方案

## 1. 概述

本文档详细描述了在trusty-tee中实现GP TEE标准`TEE_CheckMemoryAccessRights`函数的设计方案。该方案基于现有的trusty-tee基础设施，无需添加新的系统调用，通过复用现有的内存管理和MMU查询机制来实现完整的内存访问权限检查功能。

## 2. 设计目标

- **安全性**：确保内存访问权限检查的准确性和安全性
- **性能**：针对不同大小的内存区域提供优化的检查策略
- **兼容性**：符合GP TEE标准规范，提供标准接口
- **可维护性**：模块化设计，代码结构清晰
- **无侵入性**：不修改现有系统调用，完全基于现有基础设施

## 3. 现有基础设施分析

### 3.1 核心函数分析

#### valid_address()
- **位置**：`kernel/rctee/lib/rctee/rctee_core/syscall.c`
- **功能**：验证地址范围的有效性和映射状态
- **实现**：页面级检查，使用`is_user_address()`和`vaddr_to_paddr()`

#### arch_mmu_query()
- **位置**：`kernel/lk/arch/*/mmu.c`
- **功能**：查询虚拟地址的物理映射和权限标志
- **支持架构**：ARM64、ARM32、x86_64、x86_32

#### is_user_address()
- **位置**：`kernel/lk/include/kernel/vm.h`
- **功能**：检查地址是否在用户空间范围内

### 3.2 权限标志体系

#### trusty-tee架构标志
```c
#define ARCH_MMU_FLAG_PERM_USER         (1U<<2)  // 用户权限
#define ARCH_MMU_FLAG_PERM_RO           (1U<<3)  // 只读权限
#define ARCH_MMU_FLAG_PERM_NO_EXECUTE   (1U<<4)  // 禁止执行
```

#### TEE标准权限标志（新增）
```c
#define TEE_MEMORY_ACCESS_READ          0x00000001
#define TEE_MEMORY_ACCESS_WRITE         0x00000002
#define TEE_MEMORY_ACCESS_ANY_OWNER     0x00000004
```

## 4. 核心设计架构

### 4.1 四层检查机制

#### 第一层：基础参数验证
- 空指针检查
- 零大小检查
- 权限标志有效性验证
- 地址溢出检查
- 大小限制检查

#### 第二层：用户空间地址验证
- 复用`valid_address()`函数
- 页面级地址有效性检查
- 内存映射状态验证

#### 第三层：MMU权限检查
- 使用`arch_mmu_query()`查询页面权限
- 权限标志转换和匹配
- 跨页面边界处理

#### 第四层：TA上下文检查
- 参数冲突检查
- 堆内存重叠验证
- TA状态验证

### 4.2 函数接口设计

```c
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, 
                                       void *buffer, 
                                       size_t size);

TEE_Result __GP11_TEE_CheckMemoryAccessRights(uint32_t accessFlags,
                                               void *buffer,
                                               uint32_t size);
```

## 5. 详细实现设计

### 5.1 主函数实现逻辑

```c
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size) {
    // 1. 参数验证
    if (!buffer || size == 0) return TEE_ERROR_BAD_PARAMETERS;
    if (!(accessFlags & (TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE))) 
        return TEE_ERROR_BAD_PARAMETERS;
    
    // 2. 溢出检查
    if ((uintptr_t)buffer + size < (uintptr_t)buffer) 
        return TEE_ERROR_BAD_PARAMETERS;
    
    // 3. 大小限制
    if (size > TEE_MAX_MEMORY_CHECK_SIZE) 
        return TEE_ERROR_BAD_PARAMETERS;
    
    // 4. 状态检查
    if (!rctee_app_get_current() || !get_current_thread()->aspace) 
        return TEE_ERROR_BAD_STATE;
    
    // 5. 执行检查
    if (size <= PAGE_SIZE) {
        return fast_path_check(buffer, size, accessFlags);
    } else {
        return handle_cross_page_boundary(buffer, size, accessFlags);
    }
}
```

### 5.2 核心辅助函数

#### 权限验证函数
```c
static TEE_Result verify_access_permissions(void *buffer, size_t size, uint32_t accessFlags) {
    vmm_aspace_t *aspace = get_current_thread()->aspace;
    vaddr_t page_start = round_down((vaddr_t)buffer, PAGE_SIZE);
    vaddr_t page_end = round_up((vaddr_t)buffer + size, PAGE_SIZE);
    
    for (vaddr_t page_addr = page_start; page_addr < page_end; page_addr += PAGE_SIZE) {
        uint arch_flags;
        paddr_t paddr;
        
        status_t ret = arch_mmu_query(&aspace->arch_aspace, page_addr, &paddr, &arch_flags);
        if (ret != NO_ERROR) return TEE_ERROR_ACCESS_DENIED;
        
        // 检查用户权限
        if (!(arch_flags & ARCH_MMU_FLAG_PERM_USER)) 
            return TEE_ERROR_ACCESS_DENIED;
        
        // 检查写权限
        if ((accessFlags & TEE_MEMORY_ACCESS_WRITE) && (arch_flags & ARCH_MMU_FLAG_PERM_RO)) 
            return TEE_ERROR_ACCESS_DENIED;
    }
    
    return TEE_SUCCESS;
}
```

#### 跨页面边界处理
```c
static TEE_Result handle_cross_page_boundary(void *buffer, size_t size, uint32_t accessFlags) {
    vaddr_t start_page = round_down((vaddr_t)buffer, PAGE_SIZE);
    vaddr_t end_page = round_down((vaddr_t)buffer + size - 1, PAGE_SIZE);
    size_t page_count = (end_page - start_page) / PAGE_SIZE + 1;
    
    // 分批处理大内存区域
    for (size_t i = 0; i < page_count; i += TEE_MAX_PAGES_PER_BATCH) {
        size_t batch_pages = MIN(TEE_MAX_PAGES_PER_BATCH, page_count - i);
        vaddr_t batch_start = start_page + i * PAGE_SIZE;
        size_t batch_size = batch_pages * PAGE_SIZE;
        
        TEE_Result ret = verify_access_permissions((void*)batch_start, batch_size, accessFlags);
        if (ret != TEE_SUCCESS) return ret;
        
        // 避免长时间占用CPU
        if (i + TEE_MAX_PAGES_PER_BATCH < page_count) {
            thread_yield();
        }
    }
    
    return TEE_SUCCESS;
}
```

## 6. 错误处理设计

### 6.1 错误分类

| 错误类型 | 返回值 | 描述 |
|---------|--------|------|
| 参数错误 | TEE_ERROR_BAD_PARAMETERS | 空指针、无效大小、无效权限标志 |
| 访问拒绝 | TEE_ERROR_ACCESS_DENIED | 权限不足、地址无效 |
| 状态错误 | TEE_ERROR_BAD_STATE | TA状态异常、线程状态异常 |
| 系统错误 | TEE_ERROR_GENERIC | 其他系统级错误 |

### 6.2 边界情况处理

- **地址溢出**：检查`(uintptr_t)buffer + size < (uintptr_t)buffer`
- **跨页面边界**：按页面对齐处理，确保完整覆盖
- **大内存区域**：分批处理，避免长时间阻塞
- **MMU查询失败**：根据具体错误码进行分类处理

## 7. 集成方案

### 7.1 文件结构
```
user/base/lib/libutee/
├── include/
│   ├── tee_api_defines.h          # 添加权限标志定义
│   ├── tee_internal_api.h         # 添加函数声明
│   └── tee_memory_internal.h      # 内部辅助函数声明
├── src/
│   ├── tee_memory_check.c         # 主要实现文件
│   └── tee_memory_utils.c         # 辅助函数实现
└── rules.mk                       # 添加新源文件
```

### 7.2 编译配置
```makefile
MODULE_SRCS := \
    $(LOCAL_DIR)/user_header.c \
    $(LOCAL_DIR)/tee_api_property.c \
    $(LOCAL_DIR)/tee_memory_check.c \
    $(LOCAL_DIR)/tee_memory_utils.c

MODULE_INCLUDES += \
    kernel/lk/include \
    kernel/rctee/include
```

## 8. 性能优化

### 8.1 快速路径
- 小内存区域（≤ PAGE_SIZE）使用单页面检查
- 避免不必要的循环和计算

### 8.2 分批处理
- 大内存区域分批检查（每批最多256页）
- 在批次间调用`thread_yield()`避免长时间占用CPU

### 8.3 缓存优化
- 利用页面对齐减少MMU查询次数
- 合理的内存访问模式

## 9. 测试方案

### 9.1 单元测试
- 参数验证测试
- 权限检查测试
- 边界情况测试
- 性能测试

### 9.2 集成测试
- 与现有TA应用的兼容性测试
- 多线程环境测试
- 压力测试

## 10. 技术优势

1. **无新增系统调用**：完全基于现有基础设施
2. **架构一致性**：遵循trusty-tee设计原则
3. **性能优化**：针对不同场景的优化策略
4. **安全可靠**：多层检查机制确保安全性
5. **标准兼容**：符合GP TEE规范要求

## 11. 总结

本设计方案通过深入分析trusty-tee现有的内存管理基础设施，设计了一个完整的`TEE_CheckMemoryAccessRights`实现方案。该方案无需修改现有系统调用，通过复用`valid_address()`、`arch_mmu_query()`等现有函数，实现了符合GP TEE标准的内存访问权限检查功能。

设计方案具有良好的性能特性、健壮的错误处理机制和清晰的模块化结构，为trusty-tee提供了一个安全、高效、可维护的内存访问权限检查解决方案。
