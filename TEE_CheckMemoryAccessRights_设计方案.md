# TEE_CheckMemoryAccessRights 设计方案（与OP-TEE对齐）

## 1. 概述

本文档描述了在trusty-tee中实现GP TEE标准`TEE_CheckMemoryAccessRights`函数的简化设计方案。该方案与OP-TEE实现对齐，采用最小化检查策略，无需添加新的系统调用，通过复用现有的内存管理机制实现基本的内存访问权限检查功能。

## 2. 设计原则

- **与OP-TEE对齐**：参考OP-TEE的实现逻辑和检查策略
- **简化实现**：去除多余检查，只保留必要的安全验证
- **性能优先**：最小化检查开销，快速返回结果
- **兼容性**：符合GP TEE标准规范
- **无侵入性**：不修改现有系统调用，完全基于现有基础设施

## 3. OP-TEE实现分析

### 3.1 OP-TEE的简化实现逻辑

基于OP-TEE的实现，`TEE_CheckMemoryAccessRights`采用以下简化策略：

```c
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size) {
    // 1. 零大小直接返回成功
    if (!size)
        return TEE_SUCCESS;

    // 2. 基本参数检查
    if (!buffer)
        return TEE_ERROR_BAD_PARAMETERS;

    // 3. 简单的用户空间地址验证
    if (!is_user_address_range(buffer, size))
        return TEE_ERROR_ACCESS_DENIED;

    // 4. 检查是否为TA参数内存
    if (is_ta_param_buffer(buffer, size))
        return TEE_SUCCESS;

    return TEE_SUCCESS;
}
```

### 3.2 trusty-tee可复用的基础设施

#### valid_address()
- **位置**：`kernel/rctee/lib/rctee/rctee_core/syscall.c`
- **功能**：验证地址范围的有效性和映射状态
- **用途**：替代复杂的MMU查询，提供基础地址验证

#### is_user_address()
- **位置**：`kernel/lk/include/kernel/vm.h`
- **功能**：检查地址是否在用户空间范围内
- **用途**：快速验证地址范围合法性

### 3.3 权限标志对比与转换

#### GP TEE标准权限标志
```c
#define TEE_MEMORY_ACCESS_READ          0x00000001
#define TEE_MEMORY_ACCESS_WRITE         0x00000002
#define TEE_MEMORY_ACCESS_ANY_OWNER     0x00000004
```

#### trusty-tee MMU权限标志
```c
#define ARCH_MMU_FLAG_PERM_USER         (1U<<2)  // 0x00000004
#define ARCH_MMU_FLAG_PERM_RO           (1U<<3)  // 0x00000008
#define ARCH_MMU_FLAG_PERM_NO_EXECUTE   (1U<<4)  // 0x00000010
```

#### 权限映射关系
- **读权限**：GP的`TEE_MEMORY_ACCESS_READ` → trusty的`ARCH_MMU_FLAG_PERM_USER`且非`ARCH_MMU_FLAG_PERM_NO_EXECUTE`
- **写权限**：GP的`TEE_MEMORY_ACCESS_WRITE` → trusty的`ARCH_MMU_FLAG_PERM_USER`且非`ARCH_MMU_FLAG_PERM_RO`
- **用户权限**：GP的`TEE_MEMORY_ACCESS_ANY_OWNER` → trusty的`ARCH_MMU_FLAG_PERM_USER`

## 4. OP-TEE对齐的三步检查架构

### 4.1 OP-TEE标准三步检查机制

#### 第一步：零大小检查
- 如果`size`为0，直接返回`TEE_SUCCESS`
- 这是OP-TEE的标准行为

#### 第二步：内存映射权限检查
- 先把用户的accessFlags存储起来
- 获取内核中MMU的对应权限
- 将获取的MMU权限与先前存储的flags进行比较判断

#### 第三步：参数权限检查
- 调用`check_mem_access_rights_params()`检查与输入参数的冲突
- 确保不与TA的输入参数产生冲突

### 4.2 函数接口设计

```c
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags,
                                       void *buffer,
                                       size_t size);

TEE_Result __GP11_TEE_CheckMemoryAccessRights(uint32_t accessFlags,
                                               void *buffer,
                                               uint32_t size);
```

## 5. OP-TEE三步实现设计

### 5.1 主函数实现逻辑（按照OP-TEE的实际实现）

```c
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size) {
    uint32_t flags = accessFlags;  // 先把用户的flag存起来
    TEE_Result ret;

    // 第一步：零大小检查
    if (!size)
        return TEE_SUCCESS;

    // 第二步：内存映射权限检查 - 获取内存权限与先前存储的flags进行比较
    ret = check_memory_mapping_rights(flags, buffer, size);
    if (ret != TEE_SUCCESS)
        return ret;

    // 第三步：参数权限检查
    ret = check_mem_access_rights_params(flags, buffer, size);
    if (ret != TEE_SUCCESS)
        return ret;

    return TEE_SUCCESS;
}
```

### 5.2 第二步：内存映射权限检查实现（按照OP-TEE的实际逻辑）

```c
static TEE_Result check_memory_mapping_rights(uint32_t stored_flags, void *buffer, size_t size) {
    // 基本参数检查
    if (!buffer)
        return TEE_ERROR_BAD_PARAMETERS;

    // 地址溢出检查
    if ((uintptr_t)buffer + size < (uintptr_t)buffer)
        return TEE_ERROR_BAD_PARAMETERS;

    // 用户空间地址验证
    vaddr_t start_addr = (vaddr_t)buffer;
    vaddr_t end_addr = start_addr + size - 1;

    if (!is_user_address(start_addr) || !is_user_address(end_addr))
        return TEE_ERROR_ACCESS_DENIED;

    // 按照OP-TEE逻辑：先存储用户flags，再获取内存权限，然后比较
    vmm_aspace_t *aspace = get_current_thread()->aspace;
    vaddr_t page_start = round_down(start_addr, PAGE_SIZE);
    vaddr_t page_end = round_up(start_addr + size, PAGE_SIZE);

    for (vaddr_t page_addr = page_start; page_addr < page_end; page_addr += PAGE_SIZE) {
        uint mmu_flags;
        paddr_t paddr;

        // 获取内存的实际MMU权限
        status_t ret = arch_mmu_query(&aspace->arch_aspace, page_addr, &paddr, &mmu_flags);
        if (ret != NO_ERROR)
            return TEE_ERROR_ACCESS_DENIED;

        // 检查基本用户权限
        if (!(mmu_flags & ARCH_MMU_FLAG_PERM_USER))
            return TEE_ERROR_ACCESS_DENIED;

        // 将获取的MMU权限与先前存储的用户flags进行比较
        TEE_Result check_ret = compare_stored_flags_with_mmu_rights(stored_flags, mmu_flags);
        if (check_ret != TEE_SUCCESS)
            return check_ret;
    }

    return TEE_SUCCESS;
}

// 将先前存储的用户flags与获取的MMU权限进行比较
static TEE_Result compare_stored_flags_with_mmu_rights(uint32_t stored_flags, uint mmu_flags) {
    // 用户要求写权限，检查MMU是否允许写
    if (stored_flags & TEE_MEMORY_ACCESS_WRITE) {
        // MMU标记为只读，但用户要求写权限
        if (mmu_flags & ARCH_MMU_FLAG_PERM_RO)
            return TEE_ERROR_ACCESS_DENIED;
    }

    // 用户要求读权限，检查MMU是否允许读
    if (stored_flags & TEE_MEMORY_ACCESS_READ) {
        // 必须有用户权限才能读
        if (!(mmu_flags & ARCH_MMU_FLAG_PERM_USER))
            return TEE_ERROR_ACCESS_DENIED;

        // 如果是纯执行页面（很少见），可能不允许读
        // 但在大多数情况下，有用户权限的页面都可读
    }

    // 用户要求ANY_OWNER权限，检查MMU是否有用户权限
    if (stored_flags & TEE_MEMORY_ACCESS_ANY_OWNER) {
        if (!(mmu_flags & ARCH_MMU_FLAG_PERM_USER))
            return TEE_ERROR_ACCESS_DENIED;
    }

    return TEE_SUCCESS;
}
```

### 5.3 第三步：参数权限检查实现

```c
static TEE_Result check_mem_access_rights_params(uint32_t accessFlags, void *buffer, size_t size) {
    // 获取当前TA的参数信息
    extern uint32_t ta_param_types;
    extern TEE_Param ta_params[TEE_NUM_PARAMS];

    uintptr_t buf_start = (uintptr_t)buffer;
    uintptr_t buf_end = buf_start + size;

    // 检查是否与任何TA参数内存冲突
    for (int i = 0; i < TEE_NUM_PARAMS; i++) {
        uint32_t param_type = TEE_PARAM_TYPE_GET(ta_param_types, i);

        if (param_type == TEE_PARAM_TYPE_MEMREF_INPUT ||
            param_type == TEE_PARAM_TYPE_MEMREF_OUTPUT ||
            param_type == TEE_PARAM_TYPE_MEMREF_INOUT) {

            uintptr_t param_start = (uintptr_t)ta_params[i].memref.buffer;
            uintptr_t param_end = param_start + ta_params[i].memref.size;

            // 检查是否有重叠 - 如果有重叠则拒绝访问
            if (buf_start < param_end && buf_end > param_start) {
                // 根据访问标志和参数类型判断是否允许
                if (param_type == TEE_PARAM_TYPE_MEMREF_INPUT) {
                    // 输入参数只允许读访问
                    if (accessFlags & TEE_MEMORY_ACCESS_WRITE)
                        return TEE_ERROR_ACCESS_DENIED;
                } else if (param_type == TEE_PARAM_TYPE_MEMREF_OUTPUT) {
                    // 输出参数只允许写访问
                    if (accessFlags & TEE_MEMORY_ACCESS_READ)
                        return TEE_ERROR_ACCESS_DENIED;
                }
                // TEE_PARAM_TYPE_MEMREF_INOUT 允许读写访问
            }
        }
    }

    return TEE_SUCCESS;
}
```

## 6. 简化错误处理

### 6.1 错误分类（与OP-TEE对齐）

| 错误类型 | 返回值 | 描述 |
|---------|--------|------|
| 参数错误 | TEE_ERROR_BAD_PARAMETERS | 空指针、地址溢出 |
| 访问拒绝 | TEE_ERROR_ACCESS_DENIED | 地址不在用户空间、地址无效 |
| 成功 | TEE_SUCCESS | 零大小、TA参数内存、有效内存 |

### 6.2 边界情况处理

- **零大小**：直接返回`TEE_SUCCESS`（与OP-TEE对齐）
- **地址溢出**：检查`(uintptr_t)buffer + size < (uintptr_t)buffer`
- **TA参数内存**：如果是TA参数，直接允许访问

## 7. 简化集成方案

### 7.1 文件结构
```
user/base/lib/libutee/
├── include/
│   ├── tee_api_defines.h          # 添加权限标志定义
│   └── tee_internal_api.h         # 添加函数声明
├── src/
│   └── tee_memory_check.c         # 单一实现文件
└── rules.mk                       # 添加新源文件
```

### 7.2 编译配置
```makefile
MODULE_SRCS := \
    $(LOCAL_DIR)/user_header.c \
    $(LOCAL_DIR)/tee_api_property.c \
    $(LOCAL_DIR)/tee_memory_check.c

MODULE_INCLUDES += \
    kernel/lk/include \
    kernel/rctee/include
```

## 8. 完整实现代码（OP-TEE三步架构）

### 8.1 头文件修改

**user/base/lib/libutee/include/tee_api_defines.h 添加：**
```c
/* Memory Access Rights Flags */
#define TEE_MEMORY_ACCESS_READ          0x00000001
#define TEE_MEMORY_ACCESS_WRITE         0x00000002
#define TEE_MEMORY_ACCESS_ANY_OWNER     0x00000004
```

**user/base/lib/libutee/include/tee_internal_api.h 添加：**
```c
/* System API - Memory Management */
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size);
TEE_Result __GP11_TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, uint32_t size);
```

### 8.2 主实现文件

**user/base/lib/libutee/tee_memory_check.c：**
```c
/*
 * Copyright 2024 NXP
 * TEE Memory Access Rights Checking Implementation (OP-TEE 3-Step Architecture)
 */

#include <tee_internal_api.h>
#include <kernel/vm.h>
#include <lib/rctee/rctee_app.h>
#include <err.h>

// 外部TA参数变量（由TA运行时提供）
extern uint32_t ta_param_types;
extern TEE_Param ta_params[TEE_NUM_PARAMS];

// 声明kernel函数
extern bool valid_address(vaddr_t addr, size_t size);

// 第二步：内存映射权限检查（按照OP-TEE的实际逻辑）
static TEE_Result check_memory_mapping_rights(uint32_t stored_flags, void *buffer, size_t size) {
    // 基本参数检查
    if (!buffer)
        return TEE_ERROR_BAD_PARAMETERS;

    // 地址溢出检查
    if ((uintptr_t)buffer + size < (uintptr_t)buffer)
        return TEE_ERROR_BAD_PARAMETERS;

    // 用户空间地址验证
    vaddr_t start_addr = (vaddr_t)buffer;
    vaddr_t end_addr = start_addr + size - 1;

    if (!is_user_address(start_addr) || !is_user_address(end_addr))
        return TEE_ERROR_ACCESS_DENIED;

    // 按照OP-TEE逻辑：先存储用户flags，再获取内存权限，然后比较
    vmm_aspace_t *aspace = get_current_thread()->aspace;
    vaddr_t page_start = round_down(start_addr, PAGE_SIZE);
    vaddr_t page_end = round_up(start_addr + size, PAGE_SIZE);

    for (vaddr_t page_addr = page_start; page_addr < page_end; page_addr += PAGE_SIZE) {
        uint mmu_flags;
        paddr_t paddr;

        // 获取内存的实际MMU权限
        status_t ret = arch_mmu_query(&aspace->arch_aspace, page_addr, &paddr, &mmu_flags);
        if (ret != NO_ERROR)
            return TEE_ERROR_ACCESS_DENIED;

        // 检查基本用户权限
        if (!(mmu_flags & ARCH_MMU_FLAG_PERM_USER))
            return TEE_ERROR_ACCESS_DENIED;

        // 将获取的MMU权限与先前存储的用户flags进行比较
        TEE_Result check_ret = compare_stored_flags_with_mmu_rights(stored_flags, mmu_flags);
        if (check_ret != TEE_SUCCESS)
            return check_ret;
    }

    return TEE_SUCCESS;
}

// 将先前存储的用户flags与获取的MMU权限进行比较
static TEE_Result compare_stored_flags_with_mmu_rights(uint32_t stored_flags, uint mmu_flags) {
    // 用户要求写权限，检查MMU是否允许写
    if (stored_flags & TEE_MEMORY_ACCESS_WRITE) {
        // MMU标记为只读，但用户要求写权限
        if (mmu_flags & ARCH_MMU_FLAG_PERM_RO)
            return TEE_ERROR_ACCESS_DENIED;
    }

    // 用户要求读权限，检查MMU是否允许读
    if (stored_flags & TEE_MEMORY_ACCESS_READ) {
        // 必须有用户权限才能读
        if (!(mmu_flags & ARCH_MMU_FLAG_PERM_USER))
            return TEE_ERROR_ACCESS_DENIED;

        // 如果是纯执行页面（很少见），可能不允许读
        // 但在大多数情况下，有用户权限的页面都可读
    }

    // 用户要求ANY_OWNER权限，检查MMU是否有用户权限
    if (stored_flags & TEE_MEMORY_ACCESS_ANY_OWNER) {
        if (!(mmu_flags & ARCH_MMU_FLAG_PERM_USER))
            return TEE_ERROR_ACCESS_DENIED;
    }

    return TEE_SUCCESS;
}

// 第三步：参数权限检查
static TEE_Result check_mem_access_rights_params(uint32_t accessFlags, void *buffer, size_t size) {
    uintptr_t buf_start = (uintptr_t)buffer;
    uintptr_t buf_end = buf_start + size;

    // 检查是否与任何TA参数内存冲突
    for (int i = 0; i < TEE_NUM_PARAMS; i++) {
        uint32_t param_type = TEE_PARAM_TYPE_GET(ta_param_types, i);

        if (param_type == TEE_PARAM_TYPE_MEMREF_INPUT ||
            param_type == TEE_PARAM_TYPE_MEMREF_OUTPUT ||
            param_type == TEE_PARAM_TYPE_MEMREF_INOUT) {

            uintptr_t param_start = (uintptr_t)ta_params[i].memref.buffer;
            uintptr_t param_end = param_start + ta_params[i].memref.size;

            // 检查是否有重叠 - 如果有重叠则拒绝访问
            if (buf_start < param_end && buf_end > param_start) {
                // 根据访问标志和参数类型判断是否允许
                if (param_type == TEE_PARAM_TYPE_MEMREF_INPUT) {
                    // 输入参数只允许读访问
                    if (accessFlags & TEE_MEMORY_ACCESS_WRITE)
                        return TEE_ERROR_ACCESS_DENIED;
                } else if (param_type == TEE_PARAM_TYPE_MEMREF_OUTPUT) {
                    // 输出参数只允许写访问
                    if (accessFlags & TEE_MEMORY_ACCESS_READ)
                        return TEE_ERROR_ACCESS_DENIED;
                }
                // TEE_PARAM_TYPE_MEMREF_INOUT 允许读写访问
            }
        }
    }

    return TEE_SUCCESS;
}

// 主函数：OP-TEE三步架构（按照实际实现）
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size) {
    uint32_t flags = accessFlags;  // 先把用户的flag存起来
    TEE_Result ret;

    // 第一步：零大小检查
    if (!size)
        return TEE_SUCCESS;

    // 第二步：内存映射权限检查 - 获取内存权限与先前存储的flags进行比较
    ret = check_memory_mapping_rights(flags, buffer, size);
    if (ret != TEE_SUCCESS)
        return ret;

    // 第三步：参数权限检查
    ret = check_mem_access_rights_params(flags, buffer, size);
    if (ret != TEE_SUCCESS)
        return ret;

    return TEE_SUCCESS;
}

/* GP 1.1 compatibility wrapper */
TEE_Result __GP11_TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, uint32_t size) {
    return TEE_CheckMemoryAccessRights(accessFlags, buffer, (size_t)size);
}
```

## 9. OP-TEE三步架构优势

1. **严格按照OP-TEE架构**：完全遵循OP-TEE的三步检查流程
2. **清晰的职责分离**：
   - 第一步：零大小快速返回
   - 第二步：内存映射权限检查
   - 第三步：参数权限冲突检查
3. **无新增系统调用**：完全基于现有基础设施
4. **标准兼容**：符合GP TEE规范要求

## 10. 三步架构详解

### 第一步：零大小检查
```c
if (!size) return TEE_SUCCESS;
```
- 与OP-TEE完全一致的行为
- 零大小内存区域被认为是安全的

### 第二步：内存映射权限检查
- 验证指针有效性和地址溢出
- 验证用户空间地址范围
- **先把用户的accessFlags存储起来**（`uint32_t flags = accessFlags`）
- **获取内核中MMU的对应权限**（使用`arch_mmu_query()`）
- **将获取的MMU权限与先前存储的flags进行比较**：
  - 存储的`TEE_MEMORY_ACCESS_WRITE` vs 获取的`ARCH_MMU_FLAG_PERM_RO`
  - 存储的`TEE_MEMORY_ACCESS_READ` vs 获取的`ARCH_MMU_FLAG_PERM_USER`
  - 存储的`TEE_MEMORY_ACCESS_ANY_OWNER` vs 获取的`ARCH_MMU_FLAG_PERM_USER`

### 第三步：参数权限检查
- 检查与TA输入参数的冲突
- 根据参数类型和访问标志判断权限
- 防止对输入参数的非法写入
- 防止对输出参数的非法读取

## 11. 总结

本设计方案严格按照OP-TEE的三步架构实现：
1. **第一步**：如果size为0，直接返回成功
2. **第二步**：内存映射权限检查
3. **第三步**：调用check_mem_access_rights_params()检查与输入参数的冲突

该方案完全与OP-TEE对齐，提供了标准的GP TEE内存访问权限检查功能。
