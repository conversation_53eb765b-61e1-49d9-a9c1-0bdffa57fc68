# TEE_CheckMemoryAccessRights 设计方案（与OP-TEE对齐）

## 1. 概述

本文档描述了在trusty-tee中实现GP TEE标准`TEE_CheckMemoryAccessRights`函数的简化设计方案。该方案与OP-TEE实现对齐，采用最小化检查策略，无需添加新的系统调用，通过复用现有的内存管理机制实现基本的内存访问权限检查功能。

## 2. 设计原则

- **与OP-TEE对齐**：参考OP-TEE的实现逻辑和检查策略
- **简化实现**：去除多余检查，只保留必要的安全验证
- **性能优先**：最小化检查开销，快速返回结果
- **兼容性**：符合GP TEE标准规范
- **无侵入性**：不修改现有系统调用，完全基于现有基础设施

## 3. OP-TEE实现分析

### 3.1 OP-TEE的三步实现逻辑

基于OP-TEE的实际实现，`TEE_CheckMemoryAccessRights`采用以下三步策略：

```c
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size) {
    uint32_t flags = accessFlags;  // 第一步：先存储用户的flag

    if (!size)
        return TEE_SUCCESS;

    // 第二步：获取MMU权限
    uint32_t mmu_flags = get_mmu_access_rights(buffer, size);

    // 第三步：把用户的flags和获取到的mmu_flags作比较
    return compare_flags(flags, mmu_flags);
}
```

### 3.2 trusty-tee可复用的基础设施

#### arch_mmu_query()
- **位置**：`kernel/lk/arch/*/mmu.c`
- **功能**：查询虚拟地址的物理映射和权限标志
- **用途**：获取内核中MMU的对应权限

#### is_user_address()
- **位置**：`kernel/lk/include/kernel/vm.h`
- **功能**：检查地址是否在用户空间范围内
- **用途**：验证地址范围合法性

### 3.3 权限标志对比与转换

#### GP TEE标准权限标志
```c
#define TEE_MEMORY_ACCESS_READ          0x00000001
#define TEE_MEMORY_ACCESS_WRITE         0x00000002
#define TEE_MEMORY_ACCESS_ANY_OWNER     0x00000004
```

#### trusty-tee MMU权限标志
```c
#define ARCH_MMU_FLAG_PERM_USER         (1U<<2)  // 0x00000004
#define ARCH_MMU_FLAG_PERM_RO           (1U<<3)  // 0x00000008
#define ARCH_MMU_FLAG_PERM_NO_EXECUTE   (1U<<4)  // 0x00000010
```

#### 权限映射关系
- **读权限**：GP的`TEE_MEMORY_ACCESS_READ` → trusty的`ARCH_MMU_FLAG_PERM_USER`且非`ARCH_MMU_FLAG_PERM_NO_EXECUTE`
- **写权限**：GP的`TEE_MEMORY_ACCESS_WRITE` → trusty的`ARCH_MMU_FLAG_PERM_USER`且非`ARCH_MMU_FLAG_PERM_RO`
- **用户权限**：GP的`TEE_MEMORY_ACCESS_ANY_OWNER` → trusty的`ARCH_MMU_FLAG_PERM_USER`

## 4. OP-TEE对齐的三步检查架构

### 4.1 OP-TEE标准三步检查机制

#### 第一步：存储用户flags
- 先把用户的`accessFlags`存储起来：`uint32_t flags = accessFlags`
- 如果`size`为0，直接返回`TEE_SUCCESS`

#### 第二步：获取MMU权限
- 获取内核中MMU的对应权限：`uint32_t mmu_flags = get_mmu_access_rights(buffer, size)`

#### 第三步：比较flags
- 把用户的`flags`和获取到的`mmu_flags`作比较
- 根据比较结果决定是否允许访问

### 4.2 函数接口设计

```c
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags,
                                       void *buffer,
                                       size_t size);

TEE_Result __GP11_TEE_CheckMemoryAccessRights(uint32_t accessFlags,
                                               void *buffer,
                                               uint32_t size);
```

## 5. OP-TEE三步实现设计

### 5.1 主函数实现逻辑（按照OP-TEE的实际三步流程）

```c
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size) {
    // 第一步：先存储用户的flag
    uint32_t flags = accessFlags;

    // 零大小检查
    if (!size)
        return TEE_SUCCESS;

    // 第二步：获取MMU权限
    uint32_t mmu_flags;
    TEE_Result ret = get_mmu_access_rights(buffer, size, &mmu_flags);
    if (ret != TEE_SUCCESS)
        return ret;

    // 第三步：把用户的flags和获取到的mmu_flags作比较
    ret = compare_access_flags(flags, mmu_flags, buffer, size);
    if (ret != TEE_SUCCESS)
        return ret;

    return TEE_SUCCESS;
}
```

### 5.2 第二步：获取MMU权限实现

```c
static TEE_Result get_mmu_access_rights(void *buffer, size_t size, uint32_t *mmu_flags) {
    // 基本参数检查
    if (!buffer || !mmu_flags)
        return TEE_ERROR_BAD_PARAMETERS;

    // 地址溢出检查
    if ((uintptr_t)buffer + size < (uintptr_t)buffer)
        return TEE_ERROR_BAD_PARAMETERS;

    // 用户空间地址验证
    vaddr_t start_addr = (vaddr_t)buffer;
    vaddr_t end_addr = start_addr + size - 1;

    if (!is_user_address(start_addr) || !is_user_address(end_addr))
        return TEE_ERROR_ACCESS_DENIED;

    // 获取MMU权限
    vmm_aspace_t *aspace = get_current_thread()->aspace;
    vaddr_t page_start = round_down(start_addr, PAGE_SIZE);
    vaddr_t page_end = round_up(start_addr + size, PAGE_SIZE);

    uint combined_flags = 0;

    for (vaddr_t page_addr = page_start; page_addr < page_end; page_addr += PAGE_SIZE) {
        uint arch_flags;
        paddr_t paddr;

        // 获取内存的实际MMU权限
        status_t ret = arch_mmu_query(&aspace->arch_aspace, page_addr, &paddr, &arch_flags);
        if (ret != NO_ERROR)
            return TEE_ERROR_ACCESS_DENIED;

        // 累积所有页面的权限标志
        combined_flags |= arch_flags;
    }

    *mmu_flags = combined_flags;
    return TEE_SUCCESS;
}
```

### 5.3 第三步：比较flags实现

```c
static TEE_Result compare_access_flags(uint32_t user_flags, uint32_t mmu_flags, void *buffer, size_t size) {
    // 检查基本用户权限
    if (!(mmu_flags & ARCH_MMU_FLAG_PERM_USER))
        return TEE_ERROR_ACCESS_DENIED;

    // 用户要求写权限，检查MMU是否允许写
    if (user_flags & TEE_MEMORY_ACCESS_WRITE) {
        // MMU标记为只读，但用户要求写权限
        if (mmu_flags & ARCH_MMU_FLAG_PERM_RO)
            return TEE_ERROR_ACCESS_DENIED;
    }

    // 用户要求读权限，检查MMU是否允许读
    if (user_flags & TEE_MEMORY_ACCESS_READ) {
        // 必须有用户权限才能读
        if (!(mmu_flags & ARCH_MMU_FLAG_PERM_USER))
            return TEE_ERROR_ACCESS_DENIED;
    }

    // 用户要求ANY_OWNER权限，检查MMU是否有用户权限
    if (user_flags & TEE_MEMORY_ACCESS_ANY_OWNER) {
        if (!(mmu_flags & ARCH_MMU_FLAG_PERM_USER))
            return TEE_ERROR_ACCESS_DENIED;
    }

    // 可选：检查与TA参数的冲突（如果需要）
    // return check_ta_param_conflicts(user_flags, buffer, size);

    return TEE_SUCCESS;
}
```

## 6. 总结

### 6.1 设计要点

- **严格按照OP-TEE的三步实现**：
  1. 零大小检查
  2. 先存储用户flags，再获取内存权限，然后比较
  3. 参数权限检查
- **正确处理GP标志与trusty MMU标志的转换**
- **复用现有的MMU查询机制**（`arch_mmu_query()`）
- **保持与GP TEE标准的兼容性**

### 6.2 实现位置

- **头文件**：`user/base/lib/libutee/include/tee_api_defines.h`（添加GP标志定义）
- **实现文件**：`user/base/lib/libutee/tee_api.c`

## 7. 简化集成方案

### 7.1 文件结构
```
user/base/lib/libutee/
├── include/
│   ├── tee_api_defines.h          # 添加权限标志定义
│   └── tee_internal_api.h         # 添加函数声明
├── src/
│   └── tee_memory_check.c         # 单一实现文件
└── rules.mk                       # 添加新源文件
```

### 7.2 编译配置
```makefile
MODULE_SRCS := \
    $(LOCAL_DIR)/user_header.c \
    $(LOCAL_DIR)/tee_api_property.c \
    $(LOCAL_DIR)/tee_memory_check.c

MODULE_INCLUDES += \
    kernel/lk/include \
    kernel/rctee/include
```

## 8. 完整实现代码（OP-TEE三步架构）

### 8.1 头文件修改

**user/base/lib/libutee/include/tee_api_defines.h 添加：**
```c
/* Memory Access Rights Flags */
#define TEE_MEMORY_ACCESS_READ          0x00000001
#define TEE_MEMORY_ACCESS_WRITE         0x00000002
#define TEE_MEMORY_ACCESS_ANY_OWNER     0x00000004
```

**user/base/lib/libutee/include/tee_internal_api.h 添加：**
```c
/* System API - Memory Management */
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size);
TEE_Result __GP11_TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, uint32_t size);
```

### 8.2 主实现文件

**user/base/lib/libutee/tee_memory_check.c：**
```c
/*
 * Copyright 2024 NXP
 * TEE Memory Access Rights Checking Implementation (OP-TEE 3-Step Architecture)
 */

#include <tee_internal_api.h>
#include <kernel/vm.h>
#include <lib/rctee/rctee_app.h>
#include <err.h>

// 外部TA参数变量（由TA运行时提供）
extern uint32_t ta_param_types;
extern TEE_Param ta_params[TEE_NUM_PARAMS];

// 声明kernel函数
extern bool valid_address(vaddr_t addr, size_t size);

// 第二步：获取MMU权限
static TEE_Result get_mmu_access_rights(void *buffer, size_t size, uint32_t *mmu_flags) {
    // 基本参数检查
    if (!buffer || !mmu_flags)
        return TEE_ERROR_BAD_PARAMETERS;

    // 地址溢出检查
    if ((uintptr_t)buffer + size < (uintptr_t)buffer)
        return TEE_ERROR_BAD_PARAMETERS;

    // 用户空间地址验证
    vaddr_t start_addr = (vaddr_t)buffer;
    vaddr_t end_addr = start_addr + size - 1;

    if (!is_user_address(start_addr) || !is_user_address(end_addr))
        return TEE_ERROR_ACCESS_DENIED;

    // 获取MMU权限
    vmm_aspace_t *aspace = get_current_thread()->aspace;
    vaddr_t page_start = round_down(start_addr, PAGE_SIZE);
    vaddr_t page_end = round_up(start_addr + size, PAGE_SIZE);

    uint combined_flags = 0;

    for (vaddr_t page_addr = page_start; page_addr < page_end; page_addr += PAGE_SIZE) {
        uint arch_flags;
        paddr_t paddr;

        // 获取内存的实际MMU权限
        status_t ret = arch_mmu_query(&aspace->arch_aspace, page_addr, &paddr, &arch_flags);
        if (ret != NO_ERROR)
            return TEE_ERROR_ACCESS_DENIED;

        // 累积所有页面的权限标志
        combined_flags |= arch_flags;
    }

    *mmu_flags = combined_flags;
    return TEE_SUCCESS;
}

// 第三步：把用户的flags和获取到的mmu_flags作比较
static TEE_Result compare_access_flags(uint32_t user_flags, uint32_t mmu_flags, void *buffer, size_t size) {
    // 检查基本用户权限
    if (!(mmu_flags & ARCH_MMU_FLAG_PERM_USER))
        return TEE_ERROR_ACCESS_DENIED;

    // 用户要求写权限，检查MMU是否允许写
    if (user_flags & TEE_MEMORY_ACCESS_WRITE) {
        // MMU标记为只读，但用户要求写权限
        if (mmu_flags & ARCH_MMU_FLAG_PERM_RO)
            return TEE_ERROR_ACCESS_DENIED;
    }

    // 用户要求读权限，检查MMU是否允许读
    if (user_flags & TEE_MEMORY_ACCESS_READ) {
        // 必须有用户权限才能读
        if (!(mmu_flags & ARCH_MMU_FLAG_PERM_USER))
            return TEE_ERROR_ACCESS_DENIED;
    }

    // 用户要求ANY_OWNER权限，检查MMU是否有用户权限
    if (user_flags & TEE_MEMORY_ACCESS_ANY_OWNER) {
        if (!(mmu_flags & ARCH_MMU_FLAG_PERM_USER))
            return TEE_ERROR_ACCESS_DENIED;
    }

    return TEE_SUCCESS;
}

// 第三步：参数权限检查
static TEE_Result check_mem_access_rights_params(uint32_t accessFlags, void *buffer, size_t size) {
    uintptr_t buf_start = (uintptr_t)buffer;
    uintptr_t buf_end = buf_start + size;

    // 检查是否与任何TA参数内存冲突
    for (int i = 0; i < TEE_NUM_PARAMS; i++) {
        uint32_t param_type = TEE_PARAM_TYPE_GET(ta_param_types, i);

        if (param_type == TEE_PARAM_TYPE_MEMREF_INPUT ||
            param_type == TEE_PARAM_TYPE_MEMREF_OUTPUT ||
            param_type == TEE_PARAM_TYPE_MEMREF_INOUT) {

            uintptr_t param_start = (uintptr_t)ta_params[i].memref.buffer;
            uintptr_t param_end = param_start + ta_params[i].memref.size;

            // 检查是否有重叠 - 如果有重叠则拒绝访问
            if (buf_start < param_end && buf_end > param_start) {
                // 根据访问标志和参数类型判断是否允许
                if (param_type == TEE_PARAM_TYPE_MEMREF_INPUT) {
                    // 输入参数只允许读访问
                    if (accessFlags & TEE_MEMORY_ACCESS_WRITE)
                        return TEE_ERROR_ACCESS_DENIED;
                } else if (param_type == TEE_PARAM_TYPE_MEMREF_OUTPUT) {
                    // 输出参数只允许写访问
                    if (accessFlags & TEE_MEMORY_ACCESS_READ)
                        return TEE_ERROR_ACCESS_DENIED;
                }
                // TEE_PARAM_TYPE_MEMREF_INOUT 允许读写访问
            }
        }
    }

    return TEE_SUCCESS;
}

// 主函数：OP-TEE三步架构（按照实际实现流程）
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size) {
    // 第一步：先存储用户的flag
    uint32_t flags = accessFlags;

    // 零大小检查
    if (!size)
        return TEE_SUCCESS;

    // 第二步：获取MMU权限
    uint32_t mmu_flags;
    TEE_Result ret = get_mmu_access_rights(buffer, size, &mmu_flags);
    if (ret != TEE_SUCCESS)
        return ret;

    // 第三步：把用户的flags和获取到的mmu_flags作比较
    ret = compare_access_flags(flags, mmu_flags, buffer, size);
    if (ret != TEE_SUCCESS)
        return ret;

    return TEE_SUCCESS;
}

/* GP 1.1 compatibility wrapper */
TEE_Result __GP11_TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, uint32_t size) {
    return TEE_CheckMemoryAccessRights(accessFlags, buffer, (size_t)size);
}
```

## 9. OP-TEE三步架构优势

1. **严格按照OP-TEE架构**：完全遵循OP-TEE的三步检查流程
2. **清晰的职责分离**：
   - 第一步：零大小快速返回
   - 第二步：内存映射权限检查
   - 第三步：参数权限冲突检查
3. **无新增系统调用**：完全基于现有基础设施
4. **标准兼容**：符合GP TEE规范要求

## 10. 三步架构详解

### 第一步：零大小检查
```c
if (!size) return TEE_SUCCESS;
```
- 与OP-TEE完全一致的行为
- 零大小内存区域被认为是安全的

### 第二步：内存映射权限检查
- 验证指针有效性和地址溢出
- 验证用户空间地址范围
- **先把用户的accessFlags存储起来**（`uint32_t flags = accessFlags`）
- **获取内核中MMU的对应权限**（使用`arch_mmu_query()`）
- **将获取的MMU权限与先前存储的flags进行比较**：
  - 存储的`TEE_MEMORY_ACCESS_WRITE` vs 获取的`ARCH_MMU_FLAG_PERM_RO`
  - 存储的`TEE_MEMORY_ACCESS_READ` vs 获取的`ARCH_MMU_FLAG_PERM_USER`
  - 存储的`TEE_MEMORY_ACCESS_ANY_OWNER` vs 获取的`ARCH_MMU_FLAG_PERM_USER`

### 第三步：参数权限检查
- 检查与TA输入参数的冲突
- 根据参数类型和访问标志判断权限
- 防止对输入参数的非法写入
- 防止对输出参数的非法读取

## 11. 总结

本设计方案严格按照OP-TEE的三步架构实现：
1. **第一步**：如果size为0，直接返回成功
2. **第二步**：内存映射权限检查
3. **第三步**：调用check_mem_access_rights_params()检查与输入参数的冲突

该方案完全与OP-TEE对齐，提供了标准的GP TEE内存访问权限检查功能。
