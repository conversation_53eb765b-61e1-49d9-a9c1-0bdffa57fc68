# TEE_CheckMemoryAccessRights 设计方案（与OP-TEE对齐）

## 1. 概述

本文档描述了在trusty-tee中实现GP TEE标准`TEE_CheckMemoryAccessRights`函数的简化设计方案。该方案与OP-TEE实现对齐，采用最小化检查策略，无需添加新的系统调用，通过复用现有的内存管理机制实现基本的内存访问权限检查功能。

## 2. 设计原则

- **与OP-TEE对齐**：参考OP-TEE的实现逻辑和检查策略
- **简化实现**：去除多余检查，只保留必要的安全验证
- **性能优先**：最小化检查开销，快速返回结果
- **兼容性**：符合GP TEE标准规范
- **无侵入性**：不修改现有系统调用，完全基于现有基础设施

## 3. OP-TEE实现分析

### 3.1 OP-TEE的简化实现逻辑

基于OP-TEE的实现，`TEE_CheckMemoryAccessRights`采用以下简化策略：

```c
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size) {
    // 1. 零大小直接返回成功
    if (!size)
        return TEE_SUCCESS;

    // 2. 基本参数检查
    if (!buffer)
        return TEE_ERROR_BAD_PARAMETERS;

    // 3. 简单的用户空间地址验证
    if (!is_user_address_range(buffer, size))
        return TEE_ERROR_ACCESS_DENIED;

    // 4. 检查是否为TA参数内存
    if (is_ta_param_buffer(buffer, size))
        return TEE_SUCCESS;

    return TEE_SUCCESS;
}
```

### 3.2 trusty-tee可复用的基础设施

#### valid_address()
- **位置**：`kernel/rctee/lib/rctee/rctee_core/syscall.c`
- **功能**：验证地址范围的有效性和映射状态
- **用途**：替代复杂的MMU查询，提供基础地址验证

#### is_user_address()
- **位置**：`kernel/lk/include/kernel/vm.h`
- **功能**：检查地址是否在用户空间范围内
- **用途**：快速验证地址范围合法性

### 3.3 权限标志定义

```c
// GP TEE标准权限标志
#define TEE_MEMORY_ACCESS_READ          0x00000001
#define TEE_MEMORY_ACCESS_WRITE         0x00000002
#define TEE_MEMORY_ACCESS_ANY_OWNER     0x00000004
```

## 4. 简化设计架构

### 4.1 三层简化检查机制

#### 第一层：基础参数验证
- 零大小检查（直接返回成功）
- 空指针检查
- 地址溢出检查

#### 第二层：用户空间验证
- 使用`is_user_address()`检查地址范围
- 使用`valid_address()`验证地址有效性

#### 第三层：TA参数检查
- 检查是否为TA参数内存区域
- 如果是参数内存，直接返回成功

### 4.2 函数接口设计

```c
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags,
                                       void *buffer,
                                       size_t size);

TEE_Result __GP11_TEE_CheckMemoryAccessRights(uint32_t accessFlags,
                                               void *buffer,
                                               uint32_t size);
```

## 5. 简化实现设计

### 5.1 主函数实现逻辑（与OP-TEE对齐）

```c
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size) {
    // 1. 零大小检查 - 与OP-TEE对齐，直接返回成功
    if (!size)
        return TEE_SUCCESS;

    // 2. 空指针检查
    if (!buffer)
        return TEE_ERROR_BAD_PARAMETERS;

    // 3. 地址溢出检查
    if ((uintptr_t)buffer + size < (uintptr_t)buffer)
        return TEE_ERROR_BAD_PARAMETERS;

    // 4. 用户空间地址验证
    if (!is_user_address_range((vaddr_t)buffer, size))
        return TEE_ERROR_ACCESS_DENIED;

    // 5. 基础地址有效性检查
    if (!valid_address((vaddr_t)buffer, size))
        return TEE_ERROR_ACCESS_DENIED;

    // 6. TA参数检查（如果是参数内存，直接允许）
    if (is_ta_param_buffer(buffer, size))
        return TEE_SUCCESS;

    return TEE_SUCCESS;
}
```

### 5.2 辅助函数实现

#### 用户空间地址范围检查
```c
static bool is_user_address_range(vaddr_t addr, size_t size) {
    vaddr_t end_addr = addr + size - 1;

    // 检查起始和结束地址都在用户空间
    return is_user_address(addr) && is_user_address(end_addr);
}
```

#### TA参数缓冲区检查
```c
static bool is_ta_param_buffer(void *buffer, size_t size) {
    // 获取当前TA的参数信息
    extern uint32_t ta_param_types;
    extern TEE_Param ta_params[TEE_NUM_PARAMS];

    uintptr_t buf_start = (uintptr_t)buffer;
    uintptr_t buf_end = buf_start + size;

    // 检查是否与任何TA参数内存重叠
    for (int i = 0; i < TEE_NUM_PARAMS; i++) {
        uint32_t param_type = TEE_PARAM_TYPE_GET(ta_param_types, i);

        if (param_type == TEE_PARAM_TYPE_MEMREF_INPUT ||
            param_type == TEE_PARAM_TYPE_MEMREF_OUTPUT ||
            param_type == TEE_PARAM_TYPE_MEMREF_INOUT) {

            uintptr_t param_start = (uintptr_t)ta_params[i].memref.buffer;
            uintptr_t param_end = param_start + ta_params[i].memref.size;

            // 检查是否有重叠
            if (buf_start < param_end && buf_end > param_start) {
                return true;
            }
        }
    }

    return false;
}
```

## 6. 简化错误处理

### 6.1 错误分类（与OP-TEE对齐）

| 错误类型 | 返回值 | 描述 |
|---------|--------|------|
| 参数错误 | TEE_ERROR_BAD_PARAMETERS | 空指针、地址溢出 |
| 访问拒绝 | TEE_ERROR_ACCESS_DENIED | 地址不在用户空间、地址无效 |
| 成功 | TEE_SUCCESS | 零大小、TA参数内存、有效内存 |

### 6.2 边界情况处理

- **零大小**：直接返回`TEE_SUCCESS`（与OP-TEE对齐）
- **地址溢出**：检查`(uintptr_t)buffer + size < (uintptr_t)buffer`
- **TA参数内存**：如果是TA参数，直接允许访问

## 7. 简化集成方案

### 7.1 文件结构
```
user/base/lib/libutee/
├── include/
│   ├── tee_api_defines.h          # 添加权限标志定义
│   └── tee_internal_api.h         # 添加函数声明
├── src/
│   └── tee_memory_check.c         # 单一实现文件
└── rules.mk                       # 添加新源文件
```

### 7.2 编译配置
```makefile
MODULE_SRCS := \
    $(LOCAL_DIR)/user_header.c \
    $(LOCAL_DIR)/tee_api_property.c \
    $(LOCAL_DIR)/tee_memory_check.c

MODULE_INCLUDES += \
    kernel/lk/include \
    kernel/rctee/include
```

## 8. 完整实现代码

### 8.1 头文件修改

**user/base/lib/libutee/include/tee_api_defines.h 添加：**
```c
/* Memory Access Rights Flags */
#define TEE_MEMORY_ACCESS_READ          0x00000001
#define TEE_MEMORY_ACCESS_WRITE         0x00000002
#define TEE_MEMORY_ACCESS_ANY_OWNER     0x00000004
```

**user/base/lib/libutee/include/tee_internal_api.h 添加：**
```c
/* System API - Memory Management */
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size);
TEE_Result __GP11_TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, uint32_t size);
```

### 8.2 主实现文件

**user/base/lib/libutee/tee_memory_check.c：**
```c
/*
 * Copyright 2024 NXP
 * TEE Memory Access Rights Checking Implementation (OP-TEE Aligned)
 */

#include <tee_internal_api.h>
#include <kernel/vm.h>
#include <lib/rctee/rctee_app.h>
#include <err.h>

// 外部TA参数变量（由TA运行时提供）
extern uint32_t ta_param_types;
extern TEE_Param ta_params[TEE_NUM_PARAMS];

// 声明kernel函数
extern bool valid_address(vaddr_t addr, size_t size);

static bool is_user_address_range(vaddr_t addr, size_t size) {
    if (size == 0) return true;

    vaddr_t end_addr = addr + size - 1;

    // 检查起始和结束地址都在用户空间
    return is_user_address(addr) && is_user_address(end_addr);
}

static bool is_ta_param_buffer(void *buffer, size_t size) {
    uintptr_t buf_start = (uintptr_t)buffer;
    uintptr_t buf_end = buf_start + size;

    // 检查是否与任何TA参数内存重叠
    for (int i = 0; i < TEE_NUM_PARAMS; i++) {
        uint32_t param_type = TEE_PARAM_TYPE_GET(ta_param_types, i);

        if (param_type == TEE_PARAM_TYPE_MEMREF_INPUT ||
            param_type == TEE_PARAM_TYPE_MEMREF_OUTPUT ||
            param_type == TEE_PARAM_TYPE_MEMREF_INOUT) {

            uintptr_t param_start = (uintptr_t)ta_params[i].memref.buffer;
            uintptr_t param_end = param_start + ta_params[i].memref.size;

            // 检查是否有重叠
            if (buf_start < param_end && buf_end > param_start) {
                return true;
            }
        }
    }

    return false;
}

TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size) {
    // 1. 零大小检查 - 与OP-TEE对齐，直接返回成功
    if (!size)
        return TEE_SUCCESS;

    // 2. 空指针检查
    if (!buffer)
        return TEE_ERROR_BAD_PARAMETERS;

    // 3. 地址溢出检查
    if ((uintptr_t)buffer + size < (uintptr_t)buffer)
        return TEE_ERROR_BAD_PARAMETERS;

    // 4. 用户空间地址验证
    if (!is_user_address_range((vaddr_t)buffer, size))
        return TEE_ERROR_ACCESS_DENIED;

    // 5. 基础地址有效性检查
    if (!valid_address((vaddr_t)buffer, size))
        return TEE_ERROR_ACCESS_DENIED;

    // 6. TA参数检查（如果是参数内存，直接允许）
    if (is_ta_param_buffer(buffer, size))
        return TEE_SUCCESS;

    return TEE_SUCCESS;
}

/* GP 1.1 compatibility wrapper */
TEE_Result __GP11_TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, uint32_t size) {
    return TEE_CheckMemoryAccessRights(accessFlags, buffer, (size_t)size);
}
```

## 9. 技术优势

1. **与OP-TEE对齐**：采用与OP-TEE相同的简化检查策略
2. **无新增系统调用**：完全基于现有基础设施
3. **性能优化**：最小化检查开销，快速返回结果
4. **简化实现**：去除多余检查，只保留必要验证
5. **标准兼容**：符合GP TEE规范要求

## 10. 总结

本设计方案与OP-TEE实现对齐，采用简化的检查策略：
- 零大小直接返回成功
- 基本的参数和地址验证
- TA参数内存的特殊处理
- 无复杂的MMU权限检查

该方案提供了一个轻量级、高性能的内存访问权限检查实现，满足GP TEE标准要求的同时保持与OP-TEE的一致性。
