# Copyright (C) 2024 The Tongsuo Project Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

LOCAL_DIR := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

# Reference to the opensource tongsuo-mini library
TONGSUO_MINI_DIR := opensource_libs/tongsuo-mini

# Module configuration
MODULE_EXPORT_INCLUDES += $(TONGSUO_MINI_DIR)/include

# Define build-time options for tongsuo-mini
# Enable core cryptographic algorithms suitable for TEE environment
TONGSUO_MINI_WITH_SM3 ?= true
TONGSUO_MINI_WITH_SM4 ?= true
TONGSUO_MINI_WITH_ASCON ?= true
TONGSUO_MINI_WITH_HMAC ?= true
TONGSUO_MINI_WITH_ASN1 ?= false
TONGSUO_MINI_WITH_OSCORE ?= false

# Export configuration for the actual tongsuo-mini library
export TONGSUO_MINI_WITH_SM3
export TONGSUO_MINI_WITH_SM4
export TONGSUO_MINI_WITH_ASCON
export TONGSUO_MINI_WITH_HMAC
export TONGSUO_MINI_WITH_ASN1
export TONGSUO_MINI_WITH_OSCORE

# Set library name
LIB_NAME := tongsuo-mini

# Include the actual tongsuo-mini implementation
include $(TONGSUO_MINI_DIR)/rules.mk 